import type { Config, Context } from "@netlify/edge-functions";

// The Netlify Edge runtime provides a global `Netlify` object at runtime, but the
// TypeScript compiler is unaware of it. We declare its shape here so that
// `strict` compilation won't fail.
declare const Netlify: {
  env: {
    get(name: string): string | undefined;
  };
};

interface StreamEvent {
  type: 'status' | 'structured-data-chunk' | 'complete' | 'error';
  phase?: 'structured-data';
  content?: string;
  progress?: number;
  metadata?: any;
  structuredData?: any;
}

interface RequestBody {
  resumeData: string; // Base64 encoded or extracted text
  resumeMimeType: string;
  jobDescription?: string;
  jobImage?: string; // Base64 encoded
  templateId: string;
  templateHtml?: string;
  userId?: string;
  letterId?: string;
  isEdit?: boolean;
  useStructuredGeneration?: boolean; // Flag for structured data generation
}

/**
 * Streams Gemini API response and processes structured data chunks
 */
async function streamGeminiStructuredResponse(
  response: Response,
  controller: ReadableStreamDefaultController,
  encoder: <PERSON>Encoder,
  onStructuredDataUpdate: (data: any) => void
): Promise<void> {
  const reader = response.body!.getReader();
  const decoder = new TextDecoder();
  let buffer = '';
  let fullContent = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        
        // Handle Gemini streaming format (NDJSON - newline-delimited JSON)
        try {
          const parsed = JSON.parse(line);
          const candidates = parsed.candidates;
          
          if (candidates && candidates[0] && candidates[0].content && candidates[0].content.parts) {
            const text = candidates[0].content.parts[0].text;
            if (text) {
              fullContent += text;
              
              // Send chunk to client
              const chunkEvent: StreamEvent = {
                type: 'structured-data-chunk',
                phase: 'structured-data',
                content: text
              };
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunkEvent)}\n\n`));
            }
          }
        } catch (e) {
          // Ignore parse errors for incomplete JSON
          console.warn('Failed to parse Gemini chunk:', e);
        }
      }
    }

    // Try to parse the complete response as JSON
    if (fullContent.trim()) {
      try {
        const structuredData = JSON.parse(fullContent.trim());
        onStructuredDataUpdate(structuredData);
      } catch (parseError) {
        console.warn('Failed to parse complete structured data:', parseError);
        // Try to extract JSON from the content if it's wrapped in markdown
        const jsonMatch = fullContent.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          try {
            const structuredData = JSON.parse(jsonMatch[1]);
            onStructuredDataUpdate(structuredData);
          } catch (e) {
            console.error('Failed to parse extracted JSON:', e);
            throw new Error('Invalid JSON response from AI');
          }
        } else {
          throw new Error('Invalid JSON response from AI');
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
}

/**
 * Prepares Gemini API request for structured data generation
 */
function prepareGeminiStructuredRequest(body: RequestBody): any {
  const date = new Date().toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
    timeZone: 'Asia/Jakarta',
  });

  const prompt = `
You are a professional job application letter writer. You will receive a resume and a job description with a specific job position title. Generate a structured JSON object for a formal job application letter (surat lamaran pekerjaan) in Bahasa Indonesia.

First, analyze the resume and job description thoroughly to identify:
1. Skills EXPLICITLY mentioned in the resume that match the job requirements
2. Skills IMPLICITLY suggested by the resume
3. Related skills in the resume that could TRANSFER to the job requirements
4. Education, certifications, and relevant experience from the resume

Then, create a structured JSON object with the following format:
{
  "metadata": {
    "generatedAt": "${new Date().toISOString()}",
    "lastModified": "${new Date().toISOString()}",
    "templateId": "${body.templateId}",
    "language": "id"
  },
  "header": {
    "date": "${date}"
  },
  "subject": {
    "prefix": "Perihal: Lamaran Pekerjaan sebagai",
    "position": "[Extract the exact position title from job description]"
  },
  "recipient": {
    "salutation": "Yth.",
    "title": "Bapak/Ibu Bagian Sumber Daya Manusia",
    "company": "[Extract company name if available, otherwise omit]",
    "address": "[Extract company address if available as array, otherwise omit]"
  },
  "body": {
    "opening": "Dengan hormat,",
    "paragraphs": [
      "[First paragraph: State purpose and position applied for]",
      "[Second paragraph: Highlight relevant education and skills]",
      "[Third paragraph: Show enthusiasm and desire to contribute]"
    ],
    "closing": "Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih."
  },
  "signature": {
    "farewell": "Hormat saya,",
    "name": "[Extract candidate name from resume]",
    "additionalInfo": "[Optional: phone/email if appropriate]"
  },
  "attachments": ["Curriculum Vitae", "Portofolio"]
}

Job Information: ${body.jobDescription ? body.jobDescription : '[A job posting image is provided. Please analyze it to identify the job requirements, responsibilities, and qualifications]'}

Guidelines:
1. Use a formal and respectful tone appropriate for Indonesian business correspondence.
2. The total character count of all paragraphs combined should be around 1300-1600 characters.
3. Be reasonable about implied skills from the resume.
4. EXTREMELY IMPORTANT: NEVER use phrases like "belum memiliki pengalaman" or mention any lack of experience.
5. Focus ONLY on the candidate's strengths and relevant experience.
6. If there are significant skill gaps, express enthusiasm for continuing professional development.
7. Extract company name and position accurately from the job information.
8. Generate exactly 3 paragraphs for the body content.

Return ONLY the JSON object without any additional commentary, markdown formatting, or code blocks.
  `;

  const parts: any[] = [{ text: prompt }];

  // Add resume data
  if (body.resumeMimeType === 'text/plain') {
    parts.push({ text: `Resume Content:\n${body.resumeData}` });
  } else {
    parts.push({
      inlineData: {
        data: body.resumeData,
        mimeType: body.resumeMimeType
      }
    });
  }

  // Add job image if provided
  if (body.jobImage) {
    parts.push({
      inlineData: {
        data: body.jobImage,
        mimeType: 'image/jpeg' // Assume JPEG for now
      }
    });
  }

  return {
    contents: [{
      role: "user",
      parts: parts
    }],
    generationConfig: {
      temperature: 0.2,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 2048,
    }
  };
}

export default async (request: Request, context: Context) => {
  try {
    const body: RequestBody = await request.json();
    const {
      resumeData,
      resumeMimeType,
      jobDescription,
      jobImage,
      templateId,
      templateHtml,
      userId,
      letterId,
      isEdit,
      useStructuredGeneration
    } = body;

    if (!resumeData) {
      return new Response(
        JSON.stringify({ error: 'Resume data is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!jobDescription && !jobImage) {
      return new Response(
        JSON.stringify({ error: 'Either job description or job image is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const encoder = new TextEncoder();
    let structuredDataComplete: any = null;

    const stream = new ReadableStream({
      async start(controller) {
        try {
          if (useStructuredGeneration) {
            // New structured data generation approach
            const statusEvent: StreamEvent = {
              type: 'status',
              phase: 'structured-data',
              content: 'Membuat data surat terstruktur...'
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(statusEvent)}\n\n`));

            // Make request to Gemini API for structured data
            const geminiRequest = prepareGeminiStructuredRequest(body);
            const geminiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${Netlify.env.get("NEXT_PUBLIC_GOOGLE_AI_API_KEY")}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(geminiRequest),
            });

            if (!geminiResponse.ok) {
              throw new Error(`Gemini API error: ${geminiResponse.statusText}`);
            }

            // Parse Gemini response and get structured data
            const geminiData = await geminiResponse.json();
            const fullText = geminiData.candidates?.[0]?.content?.parts?.[0]?.text || '';

            // Send structured data chunks during processing
            if (fullText) {
              const chunkEvent: StreamEvent = {
                type: 'structured-data-chunk',
                phase: 'structured-data',
                content: fullText
              };
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunkEvent)}\n\n`));
            }

            // Parse the structured data
            try {
              let parsedData;
              
              // Try to parse directly as JSON
              try {
                parsedData = JSON.parse(fullText.trim());
              } catch (parseError) {
                // Try to extract JSON from markdown code blocks
                const jsonMatch = fullText.match(/```json\s*([\s\S]*?)\s*```/);
                if (jsonMatch) {
                  parsedData = JSON.parse(jsonMatch[1]);
                } else {
                  // Try to find JSON object in the text
                  const jsonStart = fullText.indexOf('{');
                  const jsonEnd = fullText.lastIndexOf('}');
                  if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
                    const jsonStr = fullText.substring(jsonStart, jsonEnd + 1);
                    parsedData = JSON.parse(jsonStr);
                  } else {
                    throw new Error('Could not extract valid JSON from response');
                  }
                }
              }

              structuredDataComplete = parsedData;

            } catch (parseError) {
              console.error('Failed to parse structured data:', parseError);
              throw new Error('Invalid structured data response from AI');
            }

            // Send completion event with structured data
            const completeEvent: StreamEvent = {
              type: 'complete',
              content: 'Structured letter generation completed successfully',
              metadata: {
                templateId,
                letterId,
                userId,
                isEdit,
                timestamp: new Date().toISOString(),
                generationMode: 'structured'
              },
              structuredData: structuredDataComplete
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(completeEvent)}\n\n`));
            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            controller.close();

          } else {
            // Legacy plain text generation (fallback for backward compatibility)
            throw new Error('Legacy generation mode no longer supported in this edge function');
          }

        } catch (error) {
          console.error('Error in unified letter generation:', error);
          const errorEvent: StreamEvent = {
            type: 'error',
            content: error instanceof Error ? error.message : 'Unknown error occurred'
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorEvent)}\n\n`));
          controller.enqueue(encoder.encode('data: [DONE]\n\n'));
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        'X-Accel-Buffering': 'no', // Disable Nginx buffering
      },
    });

  } catch (error) {
    console.error('Error in structured edge function:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to process request'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}

export const config: Config = {
  path: "/api/edge/generate-letter-unified"
};