/**
 * Structured Application Letter Data Types
 * 
 * This file defines the TypeScript interfaces for structured application letter data
 * following the migration specification from AI-based HTML generation to programmatic
 * template-based approach.
 */

// Main structured letter data interface
export interface StructuredLetterData {
  // Letter metadata
  metadata: {
    generatedAt: string;
    lastModified: string;
    templateId: string;
    language: 'id' | 'en';
  };
  
  // Header information
  header: {
    date: string;           // "27 Mei 2025"
    formattedDate?: string; // For alternative formats
  };
  
  // Subject line
  subject: {
    prefix: string;         // "Perihal: <PERSON><PERSON>"
    position: string;       // "Fullstack Developer"
  };
  
  // Recipient information
  recipient: {
    salutation: string;     // "Yth."
    title: string;          // "Bapak/Ibu Bagian Sumber Daya Manusia"
    company?: string;       // "PT Example Company"
    address?: string[];     // ["Jl. Example No. 123", "Jakarta 12345"]
  };
  
  // Letter body content
  body: {
    opening: string;        // "Dengan hormat,"
    paragraphs: string[];   // Array of paragraph texts
    closing: string;        // "Atas perhatian dan waktu yang Bapak/Ibu berikan..."
  };
  
  // Signature block
  signature: {
    farewell: string;       // "Hormat saya,"
    name: string;           // "<PERSON>"
    additionalInfo?: string; // Phone, email, etc.
  };
  
  // Optional attachments mention
  attachments?: string[];   // ["Curriculum Vitae", "Portofolio"]
}

// Template data interface that matches Handlebars placeholders
export interface LetterTemplateData {
  // Formatted date
  date: string;
  
  // Subject line (combined)
  subject: string;
  
  // Recipient block as formatted lines
  recipientLines: string[];
  
  // Salutation
  salutation: string;
  
  // Body content
  opening: string;
  paragraphs: string[];
  closing: string;
  
  // Signature block
  farewell: string;
  signatureName: string;
  additionalInfo?: string;
  
  // Attachments
  attachments?: string[];
  
  // Styling classes (for template variations)
  styleClasses?: {
    header?: string;
    body?: string;
    signature?: string;
  };
  
  // Template metadata
  templateId: string;
  language: 'id' | 'en';
}

// Helper type for letter sections
export interface LetterSection {
  id: string;
  type: 'header' | 'subject' | 'recipient' | 'body' | 'signature';
  content: any;
  editable: boolean;
}

// Validation result interface
export interface LetterValidationResult {
  isValid: boolean;
  missingFields: string[];
  warnings?: string[];
}

/**
 * Convert structured letter data to template-compatible format
 * Maps the structured data to the placeholder format used in Handlebars templates
 */
export function convertToLetterTemplateData(data: StructuredLetterData): LetterTemplateData {
  // Build recipient lines array
  const recipientLines: string[] = [];
  
  if (data.recipient.salutation && data.recipient.title) {
    recipientLines.push(`${data.recipient.salutation} ${data.recipient.title}`);
  }
  
  if (data.recipient.company) {
    recipientLines.push(data.recipient.company);
  }
  
  if (data.recipient.address) {
    recipientLines.push(...data.recipient.address);
  }
  
  // Combine subject prefix and position
  const subject = data.subject.prefix ? 
    `${data.subject.prefix} ${data.subject.position}` : 
    data.subject.position;
  
  return {
    // Date
    date: data.header.formattedDate || data.header.date,
    
    // Subject
    subject,
    
    // Recipient
    recipientLines,
    salutation: data.body.opening,
    
    // Body content
    opening: data.body.opening,
    paragraphs: data.body.paragraphs,
    closing: data.body.closing,
    
    // Signature
    farewell: data.signature.farewell,
    signatureName: data.signature.name,
    additionalInfo: data.signature.additionalInfo,
    
    // Attachments
    attachments: data.attachments,
    
    // Metadata
    templateId: data.metadata.templateId,
    language: data.metadata.language,
  };
}

/**
 * Validate structured letter data
 * Checks that all required fields are present and properly formatted
 */
export function validateStructuredLetterData(data: StructuredLetterData): LetterValidationResult {
  const missingFields: string[] = [];
  const warnings: string[] = [];
  
  // Check metadata
  if (!data.metadata) {
    missingFields.push('metadata');
  } else {
    if (!data.metadata.templateId) missingFields.push('metadata.templateId');
    if (!data.metadata.language) missingFields.push('metadata.language');
    if (!data.metadata.generatedAt) missingFields.push('metadata.generatedAt');
  }
  
  // Check header
  if (!data.header?.date) {
    missingFields.push('header.date');
  }
  
  // Check subject
  if (!data.subject) {
    missingFields.push('subject');
  } else {
    if (!data.subject.position) missingFields.push('subject.position');
    if (!data.subject.prefix) warnings.push('subject.prefix is recommended');
  }
  
  // Check recipient
  if (!data.recipient) {
    missingFields.push('recipient');
  } else {
    if (!data.recipient.salutation) missingFields.push('recipient.salutation');
    if (!data.recipient.title) missingFields.push('recipient.title');
    if (!data.recipient.company) warnings.push('recipient.company is recommended');
  }
  
  // Check body
  if (!data.body) {
    missingFields.push('body');
  } else {
    if (!data.body.opening) missingFields.push('body.opening');
    if (!data.body.paragraphs || data.body.paragraphs.length === 0) {
      missingFields.push('body.paragraphs');
    }
    if (!data.body.closing) missingFields.push('body.closing');
  }
  
  // Check signature
  if (!data.signature) {
    missingFields.push('signature');
  } else {
    if (!data.signature.farewell) missingFields.push('signature.farewell');
    if (!data.signature.name) missingFields.push('signature.name');
  }
  
  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings: warnings.length > 0 ? warnings : undefined,
  };
}

/**
 * Validate template data before rendering
 * Ensures all required template placeholders have values
 */
export function validateLetterTemplateData(data: LetterTemplateData): LetterValidationResult {
  const missingFields: string[] = [];
  const warnings: string[] = [];
  
  // Required fields for template rendering
  const requiredFields: (keyof LetterTemplateData)[] = [
    'date',
    'subject', 
    'opening',
    'farewell',
    'signatureName'
  ];
  
  for (const field of requiredFields) {
    if (!data[field]) {
      missingFields.push(field);
    }
  }
  
  // Check recipient lines
  if (!data.recipientLines || data.recipientLines.length === 0) {
    missingFields.push('recipientLines');
  }
  
  // Check paragraphs
  if (!data.paragraphs || data.paragraphs.length === 0) {
    missingFields.push('paragraphs');
  }
  
  // Warnings for optional but recommended fields
  if (!data.closing) {
    warnings.push('closing paragraph is recommended');
  }
  
  if (!data.attachments || data.attachments.length === 0) {
    warnings.push('attachments list is recommended');
  }
  
  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings: warnings.length > 0 ? warnings : undefined,
  };
}

/**
 * Create a default structured letter data template
 * Useful for initialization and testing
 */
export function createDefaultStructuredLetterData(
  templateId: string = 'clean-professional',
  language: 'id' | 'en' = 'id'
): StructuredLetterData {
  const now = new Date().toISOString();
  
  return {
    metadata: {
      generatedAt: now,
      lastModified: now,
      templateId,
      language,
    },
    header: {
      date: language === 'id' ? 
        new Date().toLocaleDateString('id-ID', { 
          day: 'numeric', 
          month: 'long', 
          year: 'numeric' 
        }) :
        new Date().toLocaleDateString('en-US', { 
          day: 'numeric', 
          month: 'long', 
          year: 'numeric' 
        }),
    },
    subject: {
      prefix: language === 'id' ? 'Perihal: Lamaran Pekerjaan sebagai' : 'Subject: Job Application for',
      position: '',
    },
    recipient: {
      salutation: language === 'id' ? 'Yth.' : 'Dear',
      title: language === 'id' ? 'Bapak/Ibu Bagian Sumber Daya Manusia' : 'Hiring Manager',
    },
    body: {
      opening: language === 'id' ? 'Dengan hormat,' : 'Dear Sir/Madam,',
      paragraphs: [],
      closing: language === 'id' ? 
        'Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih.' :
        'Thank you for your time and consideration.',
    },
    signature: {
      farewell: language === 'id' ? 'Hormat saya,' : 'Sincerely,',
      name: '',
    },
    attachments: language === 'id' ? 
      ['Curriculum Vitae', 'Portofolio'] : 
      ['Resume', 'Portfolio'],
  };
}

/**
 * Update a specific section of the structured letter data
 * Provides type-safe updates with automatic lastModified timestamp
 */
export function updateLetterSection<K extends keyof StructuredLetterData>(
  data: StructuredLetterData,
  section: K,
  updates: Partial<StructuredLetterData[K]>
): StructuredLetterData {
  return {
    ...data,
    metadata: {
      ...data.metadata,
      lastModified: new Date().toISOString(),
    },
    [section]: {
      ...data[section],
      ...updates,
    },
  };
}

/**
 * Merge multiple structured letter data objects
 * Useful for combining user edits with generated content
 */
export function mergeStructuredLetterData(
  base: StructuredLetterData,
  updates: Partial<StructuredLetterData>
): StructuredLetterData {
  return {
    ...base,
    ...updates,
    metadata: {
      ...base.metadata,
      ...updates.metadata,
      lastModified: new Date().toISOString(),
    },
    header: {
      ...base.header,
      ...updates.header,
    },
    subject: {
      ...base.subject,
      ...updates.subject,
    },
    recipient: {
      ...base.recipient,
      ...updates.recipient,
    },
    body: {
      ...base.body,
      ...updates.body,
      paragraphs: updates.body?.paragraphs ?? base.body.paragraphs,
    },
    signature: {
      ...base.signature,
      ...updates.signature,
    },
    attachments: updates.attachments ?? base.attachments,
  };
}