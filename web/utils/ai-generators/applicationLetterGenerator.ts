/**
 * DEPRECATED: Legacy application letter generator
 *
 * This file contains the old HTML generation logic using OpenAI.
 * It's kept for backward compatibility but should not be used for new features.
 *
 * Migration Path:
 * - Use structuredLetterGenerator.ts for new structured data approach
 * - Use generate-letter-unified.ts edge function for streaming
 *
 * TODO: Remove this file once all clients migrate to structured generation
 */

import { GoogleGenAI, Part, Type } from '@google/genai';
// DEPRECATED: OpenAI import removed - this was used for legacy HTML template generation
// import OpenAI from 'openai';
import mammoth from 'mammoth';
import { captureApiError } from '@/utils/errorMonitoring';
import { applicationLetterTemplates } from '@/utils/letter-templates/applicationLetterTemplates';
import { LetterDesignOutput } from '@/utils/letter-designs/letterDesignTypes';
import { createClient } from '@/lib/supabase-server';
import { ApplicationLetterOutput } from '@/types/ApplicationLetterOutput';

// Initialize the Google Generative AI client
const googleAI = new GoogleGenAI({apiKey: process.env.NEXT_PUBLIC_GOOGLE_AI_API_KEY || ''});

// DEPRECATED: OpenAI client removed - was used for legacy HTML template generation
// const openai = new OpenAI({
//   apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '',
// });

/**
 * Converts a file to a generative part for the AI model
 * @param file - The file buffer to convert
 * @param mimeType - The MIME type of the file
 * @returns Promise with the generative part
 */
async function fileToGenerativePart(file: ArrayBuffer, mimeType: string): Promise<Part> {
  const uint8Array = new Uint8Array(file);
  return {
    inlineData: {
      data: Buffer.from(uint8Array).toString('base64'),
      mimeType
    }
  };
}

/**
 * Extracts text from a DOCX file buffer
 * @param buffer - The file buffer containing DOCX data
 * @returns Promise with the extracted text
 */
async function extractTextFromDocx(buffer: ArrayBuffer): Promise<string> {
  try {
    // Convert ArrayBuffer to Buffer for mammoth
    const nodeBuffer = Buffer.from(buffer);
    
    // Use the correct API call with arrayBuffer
    const result = await mammoth.extractRawText({
      buffer: nodeBuffer
    });
    
    return result.value || '';
  } catch (error) {
    console.error('Error extracting text from DOCX:', error);
    throw new Error('Failed to extract text from DOCX file');
  }
}



export async function generateAIApplicationLetter(
  resumeFile: { buffer: ArrayBuffer, mimeType: string },
  userId?: string,
  jobDescription?: string,
  jobImage?: { buffer: ArrayBuffer, mimeType: string },
  templateId: string = 'plain-text',
  editedLetterText?: string,
): Promise<ApplicationLetterOutput> {
  try {
    let plainText: string;

    if (editedLetterText) {
      // If edited text is provided, use it directly
      plainText = editedLetterText;
    } else {
      // If not, generate the letter from scratch
      if (!['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'image/png', 'image/jpeg', 'image/jpg'].includes(resumeFile.mimeType)) {
        throw new Error('Unsupported resume file format. Please use PDF, DOCX, PNG, JPG, or JPEG files.');
      }

      if (!jobDescription && !jobImage) {
        throw new Error('Either job description text or job posting image must be provided');
      }

      let resumePart: Part;
      let jobImagePart: Part | undefined;

      if (resumeFile.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        const extractedText = await extractTextFromDocx(resumeFile.buffer);
        resumePart = { text: extractedText };
      } else {
        resumePart = await fileToGenerativePart(resumeFile.buffer, resumeFile.mimeType);
      }

      if (jobImage) {
        if (!jobImage.mimeType.startsWith('image/')) {
          throw new Error('Unsupported job image format. Only image formats are accepted.');
        }
        jobImagePart = await fileToGenerativePart(jobImage.buffer, jobImage.mimeType);
      }

      const date = new Date().toLocaleDateString('id-ID', {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
        timeZone: 'Asia/Jakarta',
      });

      const prompt = `
You are a professional job application letter writer. You will receive a resume and a job description with a specific job position title. Craft a plain-text job application letter (surat lamaran pekerjaan) in Bahasa Indonesia that is formal. Do not include any markdown, bullet points, or headings; write only the letter text ready to copy-paste.

First, analyze the resume and job description thoroughly to identify:
1. Skills EXPLICITLY mentioned in the resume that match the job requirements
2. Skills IMPLICITLY suggested by the resume
3. Related skills in the resume that could TRANSFER to the job requirements
4. Education, certifications, and relevant experience from the resume

Then, write a formal application letter that:
- Uses proper Indonesian business letter format
- The character count of the entire letter (including spaces) should be around 1300-1600 characters, not more, not less
- Includes current date (${date}) at the top
- Addresses the HR department of the company. If the company's address is known, include it; otherwise, omit the address line.
- States the purpose of applying for the specific position clearly
- Highlights the candidate's relevant education, skills, and experiences
- Shows enthusiasm for the position and desire to contribute to the company
- Includes a polite closing asking for interview opportunity
- Uses a formal closing salutation

Job Information: ${jobDescription ? jobDescription : '[A job posting image is provided. Please analyze it to identify the job requirements, responsibilities, and qualifications]'}

Guidelines:
1. Use a formal and respectful tone appropriate for Indonesian business correspondence.
2. Include proper letter components IN ORDER: date, subject line (Perihal: Lamaran Pekerjaan sebagai [position]), recipient, body, and closing.
3. Be reasonable about implied skills from the resume.
4. EXTREMELY IMPORTANT: NEVER use phrases like "belum memiliki pengalaman" or mention any lack of experience.
5. Focus ONLY on the candidate's strengths and relevant experience.
6. If there are significant skill gaps, express enthusiasm for continuing professional development.
7. Reference the attached resume/CV for more detailed information.
8. Follow standard Indonesian formal letter structure.
9. The ENTIRE letter (including spaces) should be around 1300-1600 characters, not more, not less. Make it concise, but still formal and complete.

Return only the plain-text application letter without any additional commentary or manual edits.
      `;

      const response = await googleAI.models.generateContent({
        model: "gemini-2.0-flash",
        contents: [
          {
            role: "user",
            parts: [
              { text: prompt },
              resumePart,
              ...(jobImagePart ? [jobImagePart] : [])
            ]
          }
        ],
        config: {
          temperature: 0.2,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        },
      });

      plainText = response.text || '';
    }

    // Get the selected template by ID
    const selectedTemplate = applicationLetterTemplates.find(template => template.id === templateId);
    
    // For plain text option, don't need to process HTML
    if (!selectedTemplate) {
      // Save the letter to Supabase (only for authenticated users)
      let letterId: string | undefined;
      try {
        const supabase = await createClient();
        
        const { data: letterData, error: saveError } = await supabase
          .from('letters')
          .insert({
            user_id: userId,
            plain_text: plainText,
            design_html: null,
            template_id: templateId
          })
          .select('id')
          .single();

        if (saveError) {
          console.error('Error saving letter to database:', saveError);
          captureApiError('save-letter-to-database', saveError);
        } else {
          letterId = letterData?.id;
        }
      } catch (dbError) {
        console.error('Database error when saving letter:', dbError);
        captureApiError('database-save-letter', dbError as Error);
      }

      return {
        plainText,
        design: undefined, // No designs for plain text option
        letterId,
        price: undefined,
        currency: undefined,
      };
    }

    // DEPRECATED: OpenAI template generation removed
    // This legacy approach used OpenAI to generate HTML templates
    // New approach uses structured data and Handlebars templates
    
    console.warn('[DEPRECATED] Legacy HTML template generation called - use structured generation instead');
    
    // Fallback: Return empty templated HTML to maintain compatibility
    // This will cause the function to return plain text only
    let templatedHtml = '';
    
    // TODO: Remove this entire legacy path once migration is complete
    
    // Save the letter to Supabase (only for authenticated users)
    let letterId: string | undefined;
    try {
      const supabase = await createClient();
      
      const { data: letterData, error: saveError } = await supabase
        .from('letters')
        .insert({
          user_id: userId,
          plain_text: plainText,
          design_html: templatedHtml || null,
          template_id: templateId
        })
        .select('id')
        .single();

      if (saveError) {
        console.error('Error saving letter to database:', saveError);
        captureApiError('save-letter-to-database', saveError);
      } else {
        letterId = letterData?.id;
      }
    } catch (dbError) {
      console.error('Database error when saving letter:', dbError);
      captureApiError('database-save-letter', dbError as Error);
    }

    // Validate the templated HTML
    if (templatedHtml && templatedHtml.trim() !== '' &&
        templatedHtml.trim().startsWith('<') && templatedHtml.includes('>')) {
      // Return both plain text and designs
      return {
        plainText,
        design: {
          templateId: selectedTemplate?.id || '',
          html: templatedHtml,
          name: selectedTemplate?.name || '',
          previewDescription: selectedTemplate?.previewDescription || '',
          fontFamily: selectedTemplate?.fontFamily || 'Arial, sans-serif',
          recommended: selectedTemplate?.recommended || false
        },
        letterId,
        price: 15000, // Fixed price for letter generation
        currency: 'IDR'
      };
    }

    return {
      plainText,
      design: undefined,
      letterId,
      price: undefined,
      currency: undefined
    };
  } catch (error) {
    console.error('Error generating application letter with AI:', error);
    
    // Log error to Rollbar
    captureApiError('generate-application-letter', error, {
      resumeType: resumeFile.mimeType,
      hasJobDescription: !!jobDescription,
      hasJobImage: !!jobImage
    });
    
    throw new Error('Failed to generate application letter using AI');
  }
}
