/**
 * Application letter template collection
 * Contains predefined HTML templates for application letters using Handlebars
 */

import { LetterTemplateData, StructuredLetterData } from '../../types/letter-structured';
import { fillLetterTemplate } from '../letter-template-engine';

export interface LetterTemplate {
  id: string;
  name: string;
  previewDescription: string;
  templateHtml: string | ((data: StructuredLetterData) => string);
  previewImagePath: string;
  isPremium: boolean;
  recommended: boolean;
  tokenCost: number;
  fontFamily: string;
}

// Helper function to render template with structured data using the new template engine
function renderTemplate(templateId: string, data: StructuredLetterData): string {
  try {
    const template = getTemplateById(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }
    return fillLetterTemplate(template, data);
  } catch (error) {
    console.error(`Error rendering template ${templateId}:`, error);
    throw error; // Re-throw to maintain error context
  }
}

export const plainTextTemplate: LetterTemplate = {
    id: "plain-text",
    name: "Plain Text",
    previewDescription: "Surat lamaran dengan format teks sederhana",
    previewImagePath: "/svgs/plain_text.svg",
    isPremium: false,
    tokenCost: 10,
    recommended: false,
    fontFamily: 'Roboto, sans-serif',
    templateHtml: (data: StructuredLetterData) => renderTemplate("plain-text", data)
}

/**
 * Classic Blue Template
 * A formal business letter with blue accents
 */
export const classicBlueTemplate: LetterTemplate = {
  id: "classic-blue",
  name: "Classic Blue",
  previewDescription: "Surat formal dengan aksen biru dan layout profesional",
  previewImagePath: "/svgs/classic_blue.svg",
  isPremium: true,
  tokenCost: 15,
  recommended: false,
  fontFamily: "'Merriweather', serif",
  templateHtml: (data: StructuredLetterData) => renderTemplate("classic-blue", data)
};

/**
 * Modern Professional Template
 * A premium business letter with elegant design and PDF optimization
 */
export const professionalClassicTemplate: LetterTemplate = {
  id: "professional-classic",
  name: "Professional Classic",
  previewDescription: "Desain profesional dengan header biru dan judul tercentrasi",
  previewImagePath: "/svgs/professional_classic.svg",
  isPremium: true,
  tokenCost: 15,
  recommended: true,
  fontFamily: 'Inter, sans-serif',
  templateHtml: (data: StructuredLetterData) => renderTemplate("professional-classic", data)
};

/**
 * Minimalist Sidebar Template
 * A clean design with left sidebar accent, minimal typography, and subtle geometric elements
 */
export const minimalistSidebarTemplate: LetterTemplate = {
  id: "minimalist-sidebar",
  name: "Minimalist Sidebar",
  previewDescription: "Desain modern dengan sidebar indigo di sisi kiri, tata letak premium, dan spasi bersih",
  previewImagePath: "/svgs/minimalist_sidebar.svg",
  isPremium: true,
  tokenCost: 15,
  recommended: false,
  fontFamily: 'Source Sans Pro, sans-serif',
  templateHtml: (data: StructuredLetterData) => renderTemplate("minimalist-sidebar", data)
};

export const minimalistBorderFrameTemplate: LetterTemplate = {
    id: "minimalist-border-frame",
    name: "Minimalist Border Frame",
    previewDescription: "Desain elegan dengan bingkai border, detail sudut halus berwarna hijau, dan hierarki baris yang rapi",
    previewImagePath: "/svgs/minimalist_border.svg",
    isPremium: true,
    tokenCost: 15,
    recommended: false,
    fontFamily: 'Lato, sans-serif',
    templateHtml: (data: StructuredLetterData) => renderTemplate("minimalist-border-frame", data)
};

export const minimalistAccentTemplate: LetterTemplate = {
    id: "minimalist-accent",
    name: "Minimalist Accents",
    previewDescription: "Desain modern dan bersih dengan aksen kuning, spasi teratur, dan elemen dekoratif minimalis",
    previewImagePath: "/svgs/minimalist_accent.svg",
    isPremium: true,
    tokenCost: 15,
    recommended: false,
    fontFamily: 'IBM Plex Sans, sans-serif',
    templateHtml: (data: StructuredLetterData) => renderTemplate("minimalist-accent", data)
};

export const minimalistCircularAccentsTemplate: LetterTemplate = {
    id: "minimalist-circular",
    name: "Minimalist Circular Accents",
    previewDescription: "Desain modern dengan elemen aksen lingkaran ungu, spasi yang bersih, dan pola titik yang halus",
    previewImagePath: "/svgs/minimalist_circular_accent.svg",
    isPremium: true,
    tokenCost: 15,
    recommended: false,
    fontFamily: 'Open Sans, sans-serif',
    templateHtml: (data: StructuredLetterData) => renderTemplate("minimalist-circular-accents", data)
};

// Collection of all available templates
export const applicationLetterTemplates: LetterTemplate[] = [
  plainTextTemplate,
  classicBlueTemplate,
  professionalClassicTemplate,
  minimalistSidebarTemplate,
  minimalistBorderFrameTemplate,
  minimalistAccentTemplate,
  minimalistCircularAccentsTemplate,
];

// Get template by ID
export function getTemplateById(id: string): LetterTemplate | undefined {
  return applicationLetterTemplates.find(template => template.id === id);
}