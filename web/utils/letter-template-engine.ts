import { LetterTemplate } from './letter-templates/applicationLetterTemplates';
import { 
  StructuredLetterData, 
  LetterTemplateData, 
  convertToLetterTemplateData, 
  validateLetterTemplateData,
  validateStructuredLetterData 
} from '../types/letter-structured';

// Import precompiled templates (when available)
// These will be generated by webpack compilation
let plainTextTemplate: any;
let classicBlueTemplate: any;
let professionalClassicTemplate: any;
let minimalistSidebarTemplate: any;
let minimalistBorderFrameTemplate: any;
let minimalistAccentTemplate: any;
let minimalistCircularAccentsTemplate: any;

// Try to import precompiled templates, fall back to dynamic compilation if not available
try {
  plainTextTemplate = require('./handlebars-templates/compiled/plain-text.js');
} catch (e) {
  console.warn('Precompiled plain-text template not found, will use dynamic compilation');
}

try {
  classicBlueTemplate = require('./handlebars-templates/compiled/classic-blue.js');
} catch (e) {
  console.warn('Precompiled classic-blue template not found, will use dynamic compilation');
}

try {
  professionalClassicTemplate = require('./handlebars-templates/compiled/professional-classic.js');
} catch (e) {
  console.warn('Precompiled professional-classic template not found, will use dynamic compilation');
}

try {
  minimalistSidebarTemplate = require('./handlebars-templates/compiled/minimalist-sidebar.js');
} catch (e) {
  console.warn('Precompiled minimalist-sidebar template not found, will use dynamic compilation');
}

try {
  minimalistBorderFrameTemplate = require('./handlebars-templates/compiled/minimalist-border-frame.js');
} catch (e) {
  console.warn('Precompiled minimalist-border-frame template not found, will use dynamic compilation');
}

try {
  minimalistAccentTemplate = require('./handlebars-templates/compiled/minimalist-accent.js');
} catch (e) {
  console.warn('Precompiled minimalist-accent template not found, will use dynamic compilation');
}

try {
  minimalistCircularAccentsTemplate = require('./handlebars-templates/compiled/minimalist-circular-accents.js');
} catch (e) {
  console.warn('Precompiled minimalist-circular-accents template not found, will use dynamic compilation');
}

/**
 * Template compilation cache for performance
 * Key: template ID, Value: precompiled Handlebars template
 */
const precompiledTemplates = new Map<string, any>();

// Initialize precompiled templates when available
if (plainTextTemplate) precompiledTemplates.set('plain-text', plainTextTemplate);
if (classicBlueTemplate) precompiledTemplates.set('classic-blue', classicBlueTemplate);
if (professionalClassicTemplate) precompiledTemplates.set('professional-classic', professionalClassicTemplate);
if (minimalistSidebarTemplate) precompiledTemplates.set('minimalist-sidebar', minimalistSidebarTemplate);
if (minimalistBorderFrameTemplate) precompiledTemplates.set('minimalist-border-frame', minimalistBorderFrameTemplate);
if (minimalistAccentTemplate) precompiledTemplates.set('minimalist-accent', minimalistAccentTemplate);
if (minimalistCircularAccentsTemplate) precompiledTemplates.set('minimalist-circular-accents', minimalistCircularAccentsTemplate);

/**
 * Dynamic compilation cache for templates not precompiled
 */
const dynamicTemplateCache = new Map<string, any>();

/**
 * Get precompiled template by ID
 * @param templateId - Template ID
 * @returns Precompiled Handlebars template or null if not found
 */
function getPrecompiledTemplate(templateId: string): any {
  return precompiledTemplates.get(templateId);
}

/**
 * Compile template dynamically as fallback
 * @param templateId - Template ID
 * @returns Compiled Handlebars template
 */
function compileDynamicTemplate(templateId: string): any {
  // Check cache first
  if (dynamicTemplateCache.has(templateId)) {
    return dynamicTemplateCache.get(templateId);
  }

  try {
    const fs = require('fs');
    const path = require('path');
    const handlebars = require('handlebars');

    // Register common helpers
    handlebars.registerHelper('unless', function(this: any, conditional: any, options: any) {
      if (!conditional) {
        return options.fn(this);
      } else {
        return options.inverse(this);
      }
    });

    const templatePath = path.join(__dirname, 'handlebars-templates/letters', `${templateId}.hbs`);
    
    if (!fs.existsSync(templatePath)) {
      throw new Error(`Template file not found: ${templatePath}`);
    }

    const templateContent = fs.readFileSync(templatePath, 'utf8');
    const compiledTemplate = handlebars.compile(templateContent);
    
    // Cache the compiled template
    dynamicTemplateCache.set(templateId, compiledTemplate);
    
    return compiledTemplate;
  } catch (error) {
    throw new Error(`Failed to compile template ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get template by ID (precompiled or dynamically compiled)
 * @param templateId - Template ID
 * @returns Compiled Handlebars template
 */
function getTemplate(templateId: string): any {
  // Try precompiled first
  const precompiled = getPrecompiledTemplate(templateId);
  if (precompiled) {
    return precompiled;
  }

  // Fall back to dynamic compilation
  return compileDynamicTemplate(templateId);
}

/**
 * Validate template data and provide detailed error information
 * @param templateData - Template data to validate
 * @returns Validation result with details
 */
function validateTemplateDataWithDetails(templateData: LetterTemplateData): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Validate required fields
  const validation = validateLetterTemplateData(templateData);
  if (!validation.isValid) {
    errors.push(...validation.missingFields.map(field => `Missing required field: ${field}`));
  }
  
  // Add warnings if available
  if (validation.warnings) {
    warnings.push(...validation.warnings);
  }
  
  // Additional validation for data quality
  if (templateData.paragraphs && templateData.paragraphs.length === 0) {
    errors.push('Letter must have at least one paragraph');
  }
  
  if (templateData.recipientLines && templateData.recipientLines.length === 0) {
    errors.push('Recipient information is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Main function to fill letter template with structured data
 * @param template - Letter template object
 * @param data - Structured letter data
 * @returns Filled HTML string
 * @throws Error if template compilation fails or data validation fails
 */
export function fillLetterTemplate(template: LetterTemplate, data: StructuredLetterData): string {
  try {
    // Convert structured data to template format
    const templateData = convertToLetterTemplateData(data);
    
    // Validate template data
    const validation = validateTemplateDataWithDetails(templateData);
    
    if (!validation.isValid) {
      throw new Error(`Template data validation failed: ${validation.errors.join(', ')}`);
    }
    
    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.warn('Template data warnings:', validation.warnings);
    }
    
    // Get template (precompiled or dynamically compiled)
    const compiledTemplate = getTemplate(template.id);
    
    // Generate HTML with additional error context
    let html: string;
    try {
      html = compiledTemplate(templateData);
    } catch (renderError) {
      const renderErrorMessage = renderError instanceof Error ? renderError.message : 'Unknown render error';
      throw new Error(`Template rendering failed: ${renderErrorMessage}. This might be due to missing or malformed data in the template.`);
    }
    
    // Basic validation of generated HTML
    if (!html || html.trim().length === 0) {
      throw new Error('Generated HTML is empty');
    }
    
    if (!html.includes(templateData.signatureName)) {
      throw new Error('Generated HTML does not contain the signature name');
    }
    
    return html;
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    // Log error for debugging with more context
    console.error('Letter template fill error:', {
      templateId: template.id,
      templateName: template.name,
      error: errorMessage,
      signatureName: data?.signature?.name || 'Unknown',
      dataStructure: {
        hasMetadata: !!data?.metadata,
        hasHeader: !!data?.header,
        hasSubject: !!data?.subject,
        hasRecipient: !!data?.recipient,
        hasBody: !!data?.body,
        paragraphsCount: data?.body?.paragraphs?.length || 0,
        hasSignature: !!data?.signature,
        hasAttachments: !!data?.attachments && Array.isArray(data.attachments),
        attachmentsCount: data?.attachments?.length || 0,
      }
    });
    
    throw new Error(`Failed to fill letter template "${template.name}": ${errorMessage}`);
  }
}

/**
 * Get available letter template IDs
 */
export function getAvailableLetterTemplateIds(): string[] {
  const availableIds = new Set<string>();
  
  // Add precompiled template IDs
  precompiledTemplates.forEach((_, id) => {
    availableIds.add(id);
  });
  
  // Add known template IDs that should be available for dynamic compilation
  const knownTemplateIds = [
    'plain-text',
    'classic-blue',
    'professional-classic',
    'minimalist-sidebar',
    'minimalist-border-frame',
    'minimalist-accent',
    'minimalist-circular-accents'
  ];
  
  knownTemplateIds.forEach(id => availableIds.add(id));
  
  return Array.from(availableIds);
}

/**
 * Check if a letter template is available
 */
export function isLetterTemplateAvailable(templateId: string): boolean {
  // Check if precompiled
  if (precompiledTemplates.has(templateId)) {
    return true;
  }
  
  // Check if can be dynamically compiled
  try {
    const fs = require('fs');
    const path = require('path');
    const templatePath = path.join(__dirname, 'handlebars-templates/letters', `${templateId}.hbs`);
    return fs.existsSync(templatePath);
  } catch (error) {
    return false;
  }
}

/**
 * Test letter template compilation with sample data
 * @param template - Template to test
 * @param sampleData - Sample structured letter data
 * @returns Test result
 */
export function testLetterTemplate(template: LetterTemplate, sampleData: StructuredLetterData): {
  success: boolean;
  error?: string;
  warnings?: string[];
  htmlLength?: number;
} {
  try {
    const html = fillLetterTemplate(template, sampleData);
    const templateData = convertToLetterTemplateData(sampleData);
    const validation = validateTemplateDataWithDetails(templateData);
    
    return {
      success: true,
      warnings: validation.warnings,
      htmlLength: html.length
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}